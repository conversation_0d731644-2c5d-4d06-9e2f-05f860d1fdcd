# Serena AI Collaboration Workflow
description: Serena AI 協作工作流程標準
alwaysApply: false
---

## 工作流程階段

### 階段一：系統啟動
1. **強制啟動檢查**
   - 執行 `mcp_serena_initial_instructions`
   - 執行 `mcp_serena_check_onboarding_performed`

2. **記憶系統準備**
   - 列出可用記憶：`mcp_serena_list_memories`
   - 根據任務需求讀取相關記憶
   - 確認專案上下文

### 階段二：任務分析
1. **需求理解**
   - 分析用戶需求
   - 識別任務類型
   - 確認目標和約束

2. **資源評估**
   - 檢查現有資源
   - 分析依賴關係
   - 制定執行計劃

### 階段三：規則載入
1. **動態載入機制**
   - 根據任務類型載入對應規則
   - 避免載入不必要的規則
   - 確保規則相容性

2. **上下文優化**
   - 最小化 Token 消耗
   - 最大化相關性
   - 保持執行效率

### 階段四：執行和記錄
1. **任務執行**
   - 按計劃執行任務
   - 記錄關鍵決策
   - 處理異常情況

2. **經驗記錄**
   - 使用 `mcp_serena_write_memory` 記錄重要經驗
   - 更新專案知識庫
   - 為未來任務提供參考

## 記憶系統管理標準

### 記憶文件命名規範
- **功能記憶**：`[功能名稱]_[描述]_v[版本].md`
- **完成記錄**：`[任務名稱]_完成記錄_[日期]_v[版本].md`
- **經驗總結**：`[領域]_[經驗類型]_[狀態].md`

### 記憶內容結構
```markdown
# 記憶標題

## 創建時間
YYYY-MM-DD HH:MM:SS

## 背景說明
任務背景和需求描述

## 關鍵信息
重要的信息和決策

## 經驗教訓
重要的經驗和注意事項

## 相關資源
相關文件和參考資料
