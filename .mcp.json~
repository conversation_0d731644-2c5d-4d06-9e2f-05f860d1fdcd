{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "MeetingAssistant"]}, "time": {"command": "/Users/<USER>/Projects/MyMCP/.venv/bin/python", "args": ["-m", "mcp_server_time", "--local-timezone=Asia/Taipei"]}}}