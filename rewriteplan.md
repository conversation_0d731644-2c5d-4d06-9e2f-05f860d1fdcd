# 測試重寫計畫

## 問題分析

### **根本原因：資料庫路徑不一致**
- 執行 ` find . | grep '\.db'` 發現多個資料庫文件：
  - `prisma/prisma/test.db`
  - `prisma/test.db`
  - `prisma/dev.db`

- `tests/server/setup.ts` 使用路徑：`prisma/prisma/test.db`  
- `server/lib/database.ts` 使用路徑：`prisma/test.db`
- 導致測試清理的資料庫和路由使用的資料庫不是同一個
- 測試看到 157 個遺留項目是因為清理了錯誤的資料庫

**失敗的測試文件：**

1. `tests/server/routes/projects.test.ts` - 資料庫污染，期望空陣列但得到 157 個項目
2. `tests/server/routes/files.test.ts` - 同樣的資料庫污染問題  
3. `tests/server/db.test.ts` - 資料庫連接和隔離問題

## 重寫策略

### 階段一：修復資料庫路徑一致性 ✅

- [ ] 統一所有測試和產品代碼使用相同的資料庫路徑
- [ ] 修改 `server/lib/database.ts` 使用正確的測試資料庫路徑
- [ ] 修改 `tests/server/setup.ts` 確保路徑一致
- [ ] 驗證所有 PrismaClient 實例都指向同一個資料庫文件

### 階段二：重寫測試設定檔案

#### `tests/server/setup.ts` 完全重寫

- [ ] 簡化資料庫連接邏輯
- [ ] 使用統一的資料庫管理器 (`getPrismaClient`)  
- [ ] 強化清理機制：
  - [ ] 在每個測試前完全清空資料庫
  - [ ] 使用資料庫事務來隔離測試
  - [ ] 添加清理驗證（確認資料庫確實為空）
- [ ] 移除重複的資料庫架構創建邏輯
- [ ] 添加詳細的日誌來追蹤清理狀態
- [ ] 確保測試伺服器使用正確的資料庫連接

### 階段三：重寫失敗的測試文件

#### `tests/server/routes/projects.test.ts` 重寫  

- [ ] **完全獨立的測試結構**
  - [ ] 每個測試獨立創建和清理數據
  - [ ] 移除對全域 setup 的依賴
  - [ ] 使用本地的清理函數
- [ ] **簡化測試案例**
  - [ ] GET /api/projects 空資料庫測試
  - [ ] GET /api/projects 有資料測試  
  - [ ] POST /api/projects 創建測試
  - [ ] PUT /api/projects/:id 更新測試
  - [ ] DELETE /api/projects/:id 刪除測試
- [ ] **資料庫狀態驗證**
  - [ ] 測試開始前確認資料庫為空
  - [ ] 測試結束後確認清理完成
  - [ ] 添加資料庫項目計數驗證

#### `tests/server/routes/files.test.ts` 重寫

- [ ] **獨立的專案和檔案管理**
  - [ ] 每個測試創建自己的測試專案
  - [ ] 測試完成後完全清理
  - [ ] 不依賴其他測試的資料
- [ ] **簡化測試案例**  
  - [ ] POST /api/files 創建檔案測試
  - [ ] PUT /api/files/:id 更新檔案測試
  - [ ] 檔案驗證測試（名稱、內容格式等）
  - [ ] 錯誤處理測試
- [ ] **檔案和專案關聯測試**
  - [ ] 確保檔案正確關聯到專案
  - [ ] 測試專案刪除時檔案的級聯刪除

#### `tests/server/db.test.ts` 重寫

- [ ] **直接資料庫連接測試**
  - [ ] 使用統一的資料庫管理器
  - [ ] 測試基本 CRUD 操作
  - [ ] 測試資料庫約束和關聯
- [ ] **隔離的測試資料**
  - [ ] 每個測試創建和刪除自己的資料
  - [ ] 不影響其他測試
  - [ ] 使用唯一標識符避免衝突
- [ ] **資料庫狀態驗證**
  - [ ] 連接測試
  - [ ] 架構驗證
  - [ ] 效能測試（如果需要）

### 階段四：測試和驗證

#### 單獨測試每個重寫的文件

- [ ] `npm test tests/server/routes/projects.test.ts` 全部通過
- [ ] `npm test tests/server/routes/files.test.ts` 全部通過  
- [ ] `npm test tests/server/db.test.ts` 全部通過

#### 整合測試

- [ ] 所有測試一起執行：`npm run test:once`
- [ ] 驗證沒有資料庫污染
- [ ] 確認測試執行順序不影響結果
- [ ] 多次執行確保穩定性

#### 效能和穩定性

- [ ] 測試執行時間合理（< 30 秒）
- [ ] 連續執行 5 次都成功
- [ ] 檢查測試資料庫文件大小（應該很小）

### 階段五：文檔和維護

#### 更新文檔

- [ ] 更新 README.md 中的測試說明
- [ ] 添加測試資料庫設定說明
- [ ] 記錄常見測試問題和解決方案

#### 代碼品質

- [ ] 添加 TypeScript 類型檢查
- [ ] 代碼格式化和 linting
- [ ] 移除不必要的 console.log

## 成功指標

- ✅ 所有測試通過：Test Files 15 passed, Tests 187 passed  
- ✅ 測試資料庫完全隔離：每次測試開始都是空的
- ✅ 測試執行穩定：多次運行結果一致
- ✅ 沒有資料庫路徑衝突：所有組件使用同一個測試資料庫
- ✅ 測試性能良好：整體執行時間 < 30 秒

## 風險和備選方案

**風險：**

- 重寫過程中可能破壞現有的通過測試
- 資料庫架構可能需要重新創建

**備選方案：**  

- 如果重寫太複雜，考慮使用 in-memory SQLite
- 使用 Docker 容器隔離測試環境
- 為每個測試文件使用不同的資料庫文件

## 執行順序

1. **立即執行：**階段一（修復路徑）
2. **然後：**階段二（setup.ts 重寫）
3. **接著：**階段三（測試文件重寫），按依賴順序：
   - 先重寫 `db.test.ts`（基礎層）
   - 再重寫 `projects.test.ts`（API 層）  
   - 最後重寫 `files.test.ts`（依賴專案的 API）
4. **驗證：**階段四和五

預計總時間：2-3 小時

## Checkbox 統計

總計：66 個 checkbox 項目

- 階段一：4 個 checkbox
- 階段二：6 個 checkbox  
- 階段三：27 個 checkbox
- 階段四：9 個 checkbox
- 階段五：6 個 checkbox
- 成功指標：5 個 checkbox
- 備選方案：3 個 checkbox
- 執行順序：4 個 checkbox
- 風險管理：2 個 checkbox
