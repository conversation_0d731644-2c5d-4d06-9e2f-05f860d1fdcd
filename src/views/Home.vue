
<template>
  <div class="flex flex-col items-center justify-center min-h-screen bg-gray-100">
    <h1 class="text-4xl font-bold mb-4">UIGen Vue</h1>
    <p class="text-lg text-gray-600 mb-8">AI 驅動的 Vue 組件生成器</p>
    <router-link to="/main">
      <button class="px-6 py-3 text-lg font-semibold text-white bg-blue-500 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
        開始使用
      </button>
    </router-link>
  </div>
</template>

<script setup lang="ts">
// 這裡是 Home.vue 的腳本區塊
// 目前不需要任何邏輯
</script>

<style scoped>
/* 這裡是 Home.vue 的樣式區塊 */
</style>
