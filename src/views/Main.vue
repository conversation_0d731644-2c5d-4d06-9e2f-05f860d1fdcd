
<template>
  <div class="flex h-screen">
    <!-- 側邊欄 -->
    <aside class="w-64 bg-gray-800 text-white p-4">
      <h2 class="text-xl font-bold mb-4">專案列表</h2>
      <!-- 專案列表將會在這裡 -->
    </aside>

    <!-- 主要內容區 -->
    <main class="flex-1 p-4">
      <h1 class="text-2xl font-bold">主要內容</h1>
      <!-- 聊天介面、編輯器、預覽將會在這裡 -->
    </main>
  </div>
</template>

<script setup lang="ts">
// 這裡是 Main.vue 的腳本區塊
// 之後會加入 Pinia store 的邏輯
</script>

<style scoped>
/* 這裡是 Main.vue 的樣式區塊 */
</style>
