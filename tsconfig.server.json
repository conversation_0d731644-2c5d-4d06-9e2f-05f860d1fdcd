{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "CommonJS", "outDir": "./dist/server", "rootDir": ".", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "baseUrl": ".", "paths": {"@shared/*": ["shared/*"], "@server/*": ["server/*"]}}, "include": ["server/**/*", "shared/**/*"], "exclude": ["node_modules", "dist", "src", "tests", "**/*.test.ts", "**/*.spec.ts"]}